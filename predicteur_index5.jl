"""
PRÉDICTEUR INDEX5 - ANALYSE DES 9 POSSIBILITÉS
=============================================

Programme qui fonctionne avec entropie_baccarat_analyzer.jl pour analyser
les 9 possibilités de prédiction à la main n+1 basées sur les règles INDEX5.

Règles de transition INDEX1 :
- INDEX2 = C : INDEX1 bascule (0→1, 1→0)
- INDEX2 = A : INDEX1 reste stable (0→0, 1→1)
- INDEX2 = B : INDEX1 reste stable (0→0, 1→1)

Pour chaque possibilité, calcule les 12 métriques théoriques.
"""

using JSON
using Printf

# Inclure le fichier principal d'analyse d'entropie
include("entropie_baccarat_analyzer.jl")

"""
    generer_9_possibilites(index5_actuel::String) -> Vector{String}

Génère les 9 possibilités INDEX5 pour la main n+1 basées sur les règles de transition.

# Arguments
- `index5_actuel::String`: Valeur INDEX5 de la main n (format "INDEX1_INDEX2_INDEX3")

# Returns
- `Vector{String}`: Les 9 possibilités INDEX5 pour la main n+1
"""
function generer_9_possibilites(index5_actuel::String)
    # Parser l'INDEX5 actuel
    parts = split(index5_actuel, "_")
    if length(parts) != 3
        throw(ArgumentError("Format INDEX5 invalide: $index5_actuel"))
    end
    
    index1_actuel = parse(Int, parts[1])
    index2_actuel = parts[2]
    index3_actuel = parts[3]
    
    # Déterminer INDEX1 pour n+1 selon les règles de transition
    index1_suivant = if index2_actuel == "C"
        # INDEX2 = C : INDEX1 bascule
        1 - index1_actuel
    else
        # INDEX2 = A ou B : INDEX1 reste stable
        index1_actuel
    end
    
    # Générer les 9 possibilités : INDEX1_déterminé × INDEX2_{A,B,C} × INDEX3_{BANKER,PLAYER,TIE}
    possibilites = String[]
    
    for index2 in ["A", "B", "C"]
        for index3 in ["BANKER", "PLAYER", "TIE"]
            push!(possibilites, "$(index1_suivant)_$(index2)_$(index3)")
        end
    end
    
    return possibilites
end

"""
    calculer_metriques_pour_possibilite(sequence::Vector{String}, n::Int, possibilite::String) -> Dict{String, Float64}

Calcule les 12 métriques théoriques pour une possibilité donnée à la main n+1.

# Arguments
- `sequence::Vector{String}`: Séquence INDEX5 jusqu'à la main n
- `n::Int`: Numéro de la main actuelle
- `possibilite::String`: Valeur INDEX5 possible pour la main n+1

# Returns
- `Dict{String, Float64}`: Dictionnaire contenant les 12 métriques théoriques
"""
function calculer_metriques_pour_possibilite(sequence::Vector{String}, n::Int, possibilite::String)
    # Créer une séquence hypothétique avec la possibilité ajoutée
    sequence_hypothetique = copy(sequence[1:n])
    push!(sequence_hypothetique, possibilite)
    
    # Calculer toutes les formules pour cette séquence hypothétique
    resultats_complets = calculer_toutes_formules_pour_main_n(sequence_hypothetique, n+1)
    
    # Extraire seulement les métriques théoriques (THEO)
    metriques_theo = Dict{String, Float64}()
    
    # Mapping des noms courts vers les clés complètes
    mapping_metriques = Dict(
        "ShannonT" => "1B_Shannon_Jointe_THEO",
        "AEPT" => "2B_AEP_THEO",
        "TauxT" => "3B_Taux_Entropie_THEO",
        "MetricT" => "4B_Entropie_Metrique_THEO",
        "CondT" => "5B_Conditionnelle_THEO",
        "DivKLT" => "6B_Divergence_KL_THEO",
        "InfoMutT" => "7B_Information_Mutuelle_THEO",
        "CrossT" => "8B_Entropie_Croisee_THEO",
        "TopoT" => "9B_Entropie_Topologique_THEO",
        "BlockT" => "10B_Block_Cumulative_THEO",
        "CondDecT" => "11B_Conditionnelle_Decroissante_THEO",
        "TheoAEPT" => "12B_Theoreme_AEP_THEO"
    )
    
    # Extraire les métriques avec les noms courts
    for (nom_court, cle_complete) in mapping_metriques
        metriques_theo[nom_court] = resultats_complets[cle_complete]
    end
    
    return metriques_theo
end

"""
    analyser_9_possibilites(sequence::Vector{String}, n::Int) -> Dict{String, Dict{String, Float64}}

Analyse les 9 possibilités pour la main n+1 et calcule toutes les métriques.

# Arguments
- `sequence::Vector{String}`: Séquence INDEX5 jusqu'à la main n
- `n::Int`: Numéro de la main actuelle

# Returns
- `Dict{String, Dict{String, Float64}}`: Dictionnaire des possibilités avec leurs métriques
"""
function analyser_9_possibilites(sequence::Vector{String}, n::Int)
    if n <= 0 || n > length(sequence)
        throw(ArgumentError("Numéro de main invalide: $n"))
    end

    # Obtenir l'INDEX5 actuel
    index5_actuel = sequence[n]

    # Générer les 9 possibilités
    possibilites = generer_9_possibilites(index5_actuel)

    # Analyser chaque possibilité
    resultats = Dict{String, Dict{String, Float64}}()

    println("🔄 Analyse des 9 possibilités pour la main $(n+1)...")
    println("INDEX5 actuel (main $n): $index5_actuel")
    println()

    for (i, possibilite) in enumerate(possibilites)
        println("Possibilité $i/9: $possibilite")

        # Calculer les métriques pour cette possibilité
        metriques = calculer_metriques_pour_possibilite(sequence, n, possibilite)
        resultats[possibilite] = metriques

        # Affichage compact des métriques principales
        @printf("  CondT=%.4f, InfoMutT=%.4f, TauxT=%.4f\n",
                metriques["CondT"], metriques["InfoMutT"], metriques["TauxT"])
    end

    return resultats
end

"""
    afficher_analyse_detaillee(resultats::Dict{String, Dict{String, Float64}})

Affiche une analyse détaillée des résultats avec recommandations.
"""
function afficher_analyse_detaillee(resultats::Dict{String, Dict{String, Float64}})
    println("\n" * "="^80)
    println("📊 ANALYSE DÉTAILLÉE DES 9 POSSIBILITÉS")
    println("="^80)

    # Trier les possibilités par score de prédictibilité (CondT croissant)
    possibilites_triees = sort(collect(keys(resultats)),
                              by = p -> resultats[p]["CondT"])

    println("\n🎯 CLASSEMENT PAR PRÉDICTIBILITÉ (CondT croissant = plus prévisible):")
    println("-"^80)

    for (rang, possibilite) in enumerate(possibilites_triees)
        metriques = resultats[possibilite]

        @printf("%d. %-15s | CondT=%.4f InfoMutT=%.4f TauxT=%.4f MetricT=%.4f\n",
                rang, possibilite,
                metriques["CondT"], metriques["InfoMutT"],
                metriques["TauxT"], metriques["MetricT"])
    end

    # Analyse des métriques clés
    println("\n📈 ANALYSE DES MÉTRIQUES CLÉS:")
    println("-"^80)

    # Meilleure prédictibilité (CondT minimum)
    meilleure_possibilite = possibilites_triees[1]
    meilleure_metriques = resultats[meilleure_possibilite]

    println("🏆 MEILLEURE PRÉDICTIBILITÉ: $meilleure_possibilite")
    @printf("   CondT=%.4f (plus faible = plus prévisible)\n", meilleure_metriques["CondT"])
    @printf("   InfoMutT=%.4f (corrélation avec historique)\n", meilleure_metriques["InfoMutT"])
    @printf("   TauxT=%.4f (complexité intrinsèque)\n", meilleure_metriques["TauxT"])

    # Analyse de convergence
    convergence_scores = [resultats[p]["TheoAEPT"] for p in keys(resultats)]
    convergence_moyenne = sum(convergence_scores) / length(convergence_scores)

    println("\n🎯 CONVERGENCE THÉORIQUE:")
    @printf("   TheoAEPT moyen=%.4f (convergence vers modèle INDEX5)\n", convergence_moyenne)

    # Recommandation finale
    println("\n💡 RECOMMANDATION:")
    if meilleure_metriques["CondT"] < 0.1
        println("   ✅ PRÉDICTION RECOMMANDÉE: $meilleure_possibilite")
        println("   📊 Système très prévisible (CondT < 0.1)")
    elseif meilleure_metriques["CondT"] < 0.2
        println("   ⚠️  PRÉDICTION MODÉRÉE: $meilleure_possibilite")
        println("   📊 Système moyennement prévisible (0.1 ≤ CondT < 0.2)")
    else
        println("   ❌ ABSTENTION RECOMMANDÉE")
        println("   📊 Système trop chaotique (CondT ≥ 0.2)")
    end
end

"""
    sauvegarder_analyse(resultats::Dict{String, Dict{String, Float64}}, n::Int, filename::String = "")

Sauvegarde l'analyse complète dans un fichier.
"""
function sauvegarder_analyse(resultats::Dict{String, Dict{String, Float64}}, n::Int, filename::String = "")
    if isempty(filename)
        filename = "analyse_9_possibilites_main_$(n+1)_$(Dates.format(now(), "yyyymmdd_HHMMSS")).txt"
    end

    open(filename, "w") do file
        write(file, "ANALYSE DES 9 POSSIBILITÉS POUR LA MAIN $(n+1)\n")
        write(file, "="^80 * "\n\n")
        write(file, "Date: $(Dates.format(now(), "yyyy-mm-dd HH:MM:SS"))\n\n")

        # Trier par prédictibilité
        possibilites_triees = sort(collect(keys(resultats)),
                                  by = p -> resultats[p]["CondT"])

        write(file, "CLASSEMENT PAR PRÉDICTIBILITÉ:\n")
        write(file, "-"^40 * "\n")

        for (rang, possibilite) in enumerate(possibilites_triees)
            metriques = resultats[possibilite]
            write(file, "$rang. $possibilite\n")

            for (metrique, valeur) in sort(collect(metriques))
                write(file, "   $metrique: $valeur\n")
            end
            write(file, "\n")
        end

        # Recommandation
        meilleure = possibilites_triees[1]
        meilleure_cond = resultats[meilleure]["CondT"]

        write(file, "RECOMMANDATION:\n")
        write(file, "-"^40 * "\n")
        if meilleure_cond < 0.1
            write(file, "PRÉDICTION RECOMMANDÉE: $meilleure (CondT=$meilleure_cond)\n")
        elseif meilleure_cond < 0.2
            write(file, "PRÉDICTION MODÉRÉE: $meilleure (CondT=$meilleure_cond)\n")
        else
            write(file, "ABSTENTION RECOMMANDÉE (CondT=$meilleure_cond)\n")
        end
    end

    println("✅ Analyse sauvegardée dans: $filename")
end

"""
    main_predicteur()

Fonction principale pour utiliser le prédicteur INDEX5.
"""
function main_predicteur()
    println("🎯 PRÉDICTEUR INDEX5 - ANALYSE DES 9 POSSIBILITÉS")
    println("="^60)

    # Charger les données
    print("📂 Chemin du fichier JSON (ou Entrée pour défaut): ")
    filepath_input = strip(readline())
    default_filepath = "partie/dataset_baccarat_lupasco_20250704_092825_condensed.json"
    filepath = isempty(filepath_input) ? default_filepath : filepath_input

    if !isfile(filepath)
        println("❌ Fichier non trouvé: $filepath")
        return
    end

    try
        # Charger les données
        println("📂 Chargement des données...")
        data = load_json_data(filepath)

        if isempty(data)
            println("❌ Aucune donnée trouvée")
            return
        end

        # Sélectionner une partie
        println("\n📊 Parties disponibles:")
        for (i, game) in enumerate(data[1:min(5, length(data))])
            sequence = get(game, "index5_sequence", String[])
            println("  $i. Partie avec $(length(sequence)) mains")
        end

        print("\nChoisir une partie (1-$(min(5, length(data)))): ")
        game_choice = readline()
        game_index = tryparse(Int, game_choice)

        if game_index === nothing || game_index < 1 || game_index > min(5, length(data))
            println("❌ Choix invalide")
            return
        end

        # Extraire la séquence
        selected_game = data[game_index]
        sequence = get(selected_game, "index5_sequence", String[])

        if isempty(sequence)
            println("❌ Séquence vide")
            return
        end

        println("\n✅ Séquence chargée: $(length(sequence)) mains")

        # Choisir la main pour l'analyse
        print("\nPour quelle main analyser les possibilités (1-$(length(sequence)-1)): ")
        main_input = readline()
        n = tryparse(Int, main_input)

        if n === nothing || n < 1 || n >= length(sequence)
            println("❌ Numéro de main invalide")
            return
        end

        # Analyser les 9 possibilités
        resultats = analyser_9_possibilites(sequence, n)

        # Afficher l'analyse détaillée
        afficher_analyse_detaillee(resultats)

        # Proposer de sauvegarder
        print("\n💾 Sauvegarder l'analyse? (o/N): ")
        save_choice = lowercase(strip(readline()))

        if save_choice == "o" || save_choice == "oui"
            sauvegarder_analyse(resultats, n)
        end

    catch e
        println("❌ Erreur: $e")
    end
end

# Lancer le programme si exécuté directement
if abspath(PROGRAM_FILE) == @__FILE__
    main_predicteur()
end
